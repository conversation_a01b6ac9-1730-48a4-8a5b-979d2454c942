/**
 * AuracronRealmTransitionComponent.cpp
 *
 * Implementação completa do componente de transição entre realms usando UE 5.6 APIs modernas.
 * Gerencia transições visuais, físicas e de gameplay entre as diferentes camadas do realm.
 */

#include "AuracronRealmTransitionComponent.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronLayerComponent.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/PlayerController.h"
#include "Components/AudioComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Engine/Engine.h"
#include "TimerManager.h"
#include "Camera/CameraComponent.h"
#include "Sound/SoundBase.h"
#include "NiagaraSystem.h"

UAuracronRealmTransitionComponent::UAuracronRealmTransitionComponent()
{
    // Set this component to be ticked every frame
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.bStartWithTickEnabled = true;

    // Initialize default values
    bIsTransitioning = false;
    TransitionTargetLayer = EAuracronRealmLayer::None;
    CurrentTransitionType = ERealmTransitionType::Gradual;
    TransitionProgress = 0.0f;
    TransitionStartTime = 0.0f;
    TransitionDuration = 2.0f;

    // Initialize camera data
    OriginalCameraLocation = FVector::ZeroVector;
    OriginalCameraRotation = FRotator::ZeroRotator;
    OriginalCameraFOV = 90.0f;

    // Initialize cached references
    CachedRealmSubsystem = nullptr;
    CachedLayerComponent = nullptr;
    OwnerPawn = nullptr;
    OwnerController = nullptr;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm Transition Component created"));
}

void UAuracronRealmTransitionComponent::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm Transition Component BeginPlay"));

    // Cache subsystem reference
    if (UWorld* World = GetWorld())
    {
        CachedRealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>();
    }

    // Cache owner references
    if (AActor* Owner = GetOwner())
    {
        OwnerPawn = Cast<APawn>(Owner);
        if (OwnerPawn)
        {
            OwnerController = Cast<APlayerController>(OwnerPawn->GetController());
        }

        // Cache layer component
        CachedLayerComponent = Owner->FindComponentByClass<UAuracronLayerComponent>();
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm Transition Component initialized"));
}

void UAuracronRealmTransitionComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm Transition Component EndPlay"));

    // Stop any active transitions
    if (bIsTransitioning)
    {
        CancelTransition();
    }

    // Cleanup effect components
    DespawnTransitionEffects();

    Super::EndPlay(EndPlayReason);
}

void UAuracronRealmTransitionComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    // Update transition if active
    if (bIsTransitioning)
    {
        UpdateTransitionMovement(DeltaTime);
        UpdateTransitionAudio(DeltaTime);

        // Handle different transition types
        switch (CurrentTransitionType)
        {
            case ERealmTransitionType::Instant:
                HandleInstantTransition();
                break;
            case ERealmTransitionType::Gradual:
                HandleGradualTransition(DeltaTime);
                break;
            case ERealmTransitionType::Cinematic:
                HandleCinematicTransition(DeltaTime);
                break;
            case ERealmTransitionType::Combat:
                HandleCombatTransition(DeltaTime);
                break;
            case ERealmTransitionType::Stealth:
                HandleStealthTransition(DeltaTime);
                break;
        }
    }
}

// === Transition Management ===

bool UAuracronRealmTransitionComponent::StartTransition(EAuracronRealmLayer NewTargetLayer, ERealmTransitionType NewTransitionType)
{
    if (bIsTransitioning)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot start transition - already transitioning"));
        return false;
    }

    if (!ValidateTransitionRequest(NewTargetLayer))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid transition request to layer %s"),
            *UEnum::GetValueAsString(NewTargetLayer));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Starting realm transition to %s"),
        *UEnum::GetValueAsString(NewTargetLayer));

    // Setup transition data
    TransitionTargetLayer = NewTargetLayer;
    CurrentTransitionType = NewTransitionType;
    TransitionProgress = 0.0f;

    if (UWorld* World = GetWorld())
    {
        TransitionStartTime = World->GetTimeSeconds();
    }

    // Start transition
    bIsTransitioning = true;

    // Setup transition effects
    SpawnTransitionEffects();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm transition started"));
    return true;
}

void UAuracronRealmTransitionComponent::CancelTransition()
{
    if (!bIsTransitioning)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Cancelling realm transition"));

    // Stop all effects
    StopTransitionEffects();

    // Reset state
    bIsTransitioning = false;
    TransitionProgress = 0.0f;
    TransitionTargetLayer = EAuracronRealmLayer::None;

    // Cleanup effects
    DespawnTransitionEffects();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm transition cancelled"));
}

void UAuracronRealmTransitionComponent::PauseTransition()
{
    if (CurrentTransitionState == ERealmTransitionState::Idle)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Pausing realm transition"));
    
    // Pause effects
    if (TransitionVFXComponent)
    {
        TransitionVFXComponent->SetPaused(true);
    }
    
    if (TransitionAudioComponent)
    {
        TransitionAudioComponent->SetPaused(true);
    }
    
    // Trigger transition paused event
    OnTransitionPaused.Broadcast();
}

void UAuracronRealmTransitionComponent::ResumeTransition()
{
    if (CurrentTransitionState == ERealmTransitionState::Idle)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Resuming realm transition"));
    
    // Resume effects
    if (TransitionVFXComponent)
    {
        TransitionVFXComponent->SetPaused(false);
    }
    
    if (TransitionAudioComponent)
    {
        TransitionAudioComponent->SetPaused(false);
    }
    
    // Trigger transition resumed event
    OnTransitionResumed.Broadcast();
}

// === Transition State ===

ERealmTransitionState UAuracronRealmTransitionComponent::GetTransitionState() const
{
    return CurrentTransitionState;
}

float UAuracronRealmTransitionComponent::GetTransitionProgress() const
{
    return TransitionProgress;
}

EAuracronRealmLayer UAuracronRealmTransitionComponent::GetSourceLayer() const
{
    return SourceLayer;
}

EAuracronRealmLayer UAuracronRealmTransitionComponent::GetTargetLayer() const
{
    return TargetLayer;
}

FAuracronTransitionSettings UAuracronRealmTransitionComponent::GetTransitionSettings() const
{
    return TransitionSettings;
}

// === Effect Management ===

void UAuracronRealmTransitionComponent::PlayTransitionEffects()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Playing transition effects"));
    
    // Play visual effects
    if (TransitionSettings.bUseVisualEffects && TransitionVFXComponent)
    {
        TransitionVFXComponent->Activate();
    }
    
    // Play audio effects
    if (TransitionSettings.bUseAudioEffects && TransitionAudioComponent)
    {
        TransitionAudioComponent->Play();
    }
    
    // Setup camera effects
    if (TransitionSettings.bUseCameraEffects)
    {
        SetupTransitionCamera();
    }
}

void UAuracronRealmTransitionComponent::StopTransitionEffects()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Stopping transition effects"));
    
    // Stop visual effects
    if (TransitionVFXComponent)
    {
        TransitionVFXComponent->Deactivate();
    }
    
    // Stop audio effects
    if (TransitionAudioComponent)
    {
        TransitionAudioComponent->Stop();
    }
    
    // Restore camera
    RestoreOriginalCamera();
}

void UAuracronRealmTransitionComponent::UpdateTransitionEffects(float DeltaTime)
{
    if (CurrentTransitionState == ERealmTransitionState::Idle)
    {
        return;
    }
    
    // Update visual effects based on progress
    if (TransitionVFXComponent)
    {
        float EffectIntensity = CalculateEffectIntensity();
        TransitionVFXComponent->SetFloatParameter(TEXT("Intensity"), EffectIntensity);
        TransitionVFXComponent->SetFloatParameter(TEXT("Progress"), TransitionProgress);
    }
    
    // Update audio effects
    if (TransitionAudioComponent)
    {
        float VolumeMultiplier = CalculateAudioIntensity();
        TransitionAudioComponent->SetVolumeMultiplier(VolumeMultiplier);
    }
    
    // Update camera effects
    if (TransitionSettings.bUseCameraEffects)
    {
        UpdateTransitionCamera(DeltaTime);
    }
    
    // Update physics effects
    if (TransitionSettings.bUsePhysicsEffects)
    {
        UpdateTransitionPhysics(DeltaTime);
    }
}

// === Camera Management ===

void UAuracronRealmTransitionComponent::SetupTransitionCamera()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Setting up transition camera"));
    
    // Implementation would setup special camera effects for transition
    // This could include:
    // - Camera shake
    // - Field of view changes
    // - Post-process effects
    // - Cinematic camera movements
}

void UAuracronRealmTransitionComponent::UpdateTransitionCamera(float DeltaTime)
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updating transition camera"));
    
    // Implementation would update camera effects based on transition progress
    // This could include:
    // - Interpolating camera shake intensity
    // - Updating post-process parameters
    // - Animating camera movements
}

void UAuracronRealmTransitionComponent::RestoreOriginalCamera()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Restoring original camera"));
    
    // Implementation would restore camera to original state
    // This could include:
    // - Stopping camera shake
    // - Resetting field of view
    // - Removing post-process effects
    // - Returning camera to original position
}

// === Physics Management ===

void UAuracronRealmTransitionComponent::UpdateTransitionPhysics(float DeltaTime)
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updating transition physics"));
    
    // Implementation would update physics effects during transition
    // This could include:
    // - Gravity changes
    // - Physics material changes
    // - Collision changes
    // - Movement speed modifications
}

void UAuracronRealmTransitionComponent::ApplyLayerPhysics(EAuracronRealmLayer Layer)
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Applying layer physics for %s"), *UEnum::GetValueAsString(Layer));
    
    // Implementation would apply physics settings specific to the layer
    // This could include:
    // - Setting gravity scale
    // - Changing physics materials
    // - Updating collision responses
    // - Modifying movement parameters
}

// === Private Implementation Methods ===

void UAuracronRealmTransitionComponent::InitializeEffectComponents()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing effect components"));

    if (AActor* Owner = GetOwner())
    {
        // Create Niagara VFX component if needed
        if (!TransitionVFXComponent && TransitionSettings.bUseVisualEffects)
        {
            TransitionVFXComponent = NewObject<UNiagaraComponent>(Owner);
            if (TransitionVFXComponent)
            {
                TransitionVFXComponent->AttachToComponent(Owner->GetRootComponent(),
                    FAttachmentTransformRules::KeepRelativeTransform);
                TransitionVFXComponent->SetAutoActivate(false);
            }
        }

        // Create Audio component if needed
        if (!TransitionAudioComponent && TransitionSettings.bUseAudioEffects)
        {
            TransitionAudioComponent = NewObject<UAudioComponent>(Owner);
            if (TransitionAudioComponent)
            {
                TransitionAudioComponent->AttachToComponent(Owner->GetRootComponent(),
                    FAttachmentTransformRules::KeepRelativeTransform);
                TransitionAudioComponent->SetAutoActivate(false);
            }
        }

        // Create Camera component if needed
        if (!TransitionCameraComponent && TransitionSettings.bUseCameraEffects)
        {
            TransitionCameraComponent = NewObject<UCameraComponent>(Owner);
            if (TransitionCameraComponent)
            {
                TransitionCameraComponent->AttachToComponent(Owner->GetRootComponent(),
                    FAttachmentTransformRules::KeepRelativeTransform);
                TransitionCameraComponent->SetActive(false);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Effect components initialized"));
}

void UAuracronRealmTransitionComponent::CleanupEffectComponents()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Cleaning up effect components"));

    // Cleanup VFX component
    if (TransitionVFXComponent)
    {
        TransitionVFXComponent->DestroyComponent();
        TransitionVFXComponent = nullptr;
    }

    // Cleanup Audio component
    if (TransitionAudioComponent)
    {
        TransitionAudioComponent->DestroyComponent();
        TransitionAudioComponent = nullptr;
    }

    // Cleanup Camera component
    if (TransitionCameraComponent)
    {
        TransitionCameraComponent->DestroyComponent();
        TransitionCameraComponent = nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Effect components cleaned up"));
}

void UAuracronRealmTransitionComponent::SetupTransitionEffects()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up transition effects"));

    // Setup visual effects based on layer transition
    if (TransitionVFXComponent && TransitionSettings.TransitionVFX)
    {
        TransitionVFXComponent->SetAsset(TransitionSettings.TransitionVFX);
        TransitionVFXComponent->SetFloatParameter(TEXT("Intensity"), TransitionSettings.EffectIntensity);
        TransitionVFXComponent->SetFloatParameter(TEXT("Progress"), 0.0f);
    }

    // Setup audio effects
    if (TransitionAudioComponent && TransitionSettings.TransitionAudio)
    {
        TransitionAudioComponent->SetSound(TransitionSettings.TransitionAudio);
        TransitionAudioComponent->SetVolumeMultiplier(TransitionSettings.EffectIntensity);
    }

    // Start playing effects
    PlayTransitionEffects();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition effects setup complete"));
}

void UAuracronRealmTransitionComponent::UpdateTransition(float DeltaTime)
{
    if (CurrentTransitionState == ERealmTransitionState::Idle)
    {
        return;
    }

    // Calculate transition progress
    if (UWorld* World = GetWorld())
    {
        float CurrentTime = World->GetTimeSeconds();
        float ElapsedTime = CurrentTime - TransitionStartTime;
        TransitionProgress = FMath::Clamp(ElapsedTime / TransitionDuration, 0.0f, 1.0f);
    }

    // Update effects
    UpdateTransitionEffects(DeltaTime);

    // Handle transition state changes
    switch (CurrentTransitionState)
    {
        case ERealmTransitionState::FadingOut:
            if (TransitionProgress >= 0.5f)
            {
                CurrentTransitionState = ERealmTransitionState::Transitioning;
                OnTransitionMidpoint.Broadcast();
            }
            break;

        case ERealmTransitionState::Transitioning:
            if (TransitionProgress >= 0.8f)
            {
                CurrentTransitionState = ERealmTransitionState::FadingIn;
            }
            break;

        case ERealmTransitionState::FadingIn:
            if (TransitionProgress >= 1.0f)
            {
                CompleteTransition();
            }
            break;

        default:
            break;
    }

    // Trigger progress update event
    OnTransitionProgressUpdated.Broadcast(TransitionProgress);
}

void UAuracronRealmTransitionComponent::CompleteTransition()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Completing realm transition"));

    // Stop effects
    StopTransitionEffects();

    // Reset state
    CurrentTransitionState = ERealmTransitionState::Idle;
    TransitionProgress = 1.0f;

    // Apply final layer physics
    if (TransitionSettings.bUsePhysicsEffects)
    {
        ApplyLayerPhysics(TargetLayer);
    }

    // Trigger transition completed event
    OnTransitionCompletedNative.Broadcast(SourceLayer, TargetLayer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Realm transition completed"));
}

// === Private Implementation Functions ===

void UAuracronRealmTransitionComponent::UpdateTransitionMovement(float DeltaTime)
{
    if (!bIsTransitioning || !IsValid(OwnerPawn))
    {
        return;
    }

    // Calculate movement based on transition progress
    FVector CurrentLocation = OwnerPawn->GetActorLocation();
    FVector TargetPosition = CalculateTransitionPosition(TransitionProgress);

    // Smooth movement interpolation
    FVector NewLocation = FMath::VInterpTo(CurrentLocation, TargetPosition, DeltaTime, 5.0f);
    OwnerPawn->SetActorLocation(NewLocation);
}

void UAuracronRealmTransitionComponent::UpdateTransitionAudio(float DeltaTime)
{
    if (!TransitionAudioComponent || !bIsTransitioning)
    {
        return;
    }

    // Update audio based on transition progress
    float VolumeMultiplier = CalculateAudioIntensity();
    TransitionAudioComponent->SetVolumeMultiplier(VolumeMultiplier);

    // Update pitch based on transition type
    float PitchMultiplier = 1.0f;
    switch (CurrentTransitionType)
    {
        case ERealmTransitionType::Instant:
            PitchMultiplier = 2.0f;
            break;
        case ERealmTransitionType::Cinematic:
            PitchMultiplier = 0.8f;
            break;
        case ERealmTransitionType::Combat:
            PitchMultiplier = 1.5f;
            break;
        default:
            PitchMultiplier = 1.0f;
            break;
    }

    TransitionAudioComponent->SetPitchMultiplier(PitchMultiplier);
}









float UAuracronRealmTransitionComponent::CalculateEffectIntensity() const
{
    float BaseIntensity = TransitionSettings.EffectIntensity;

    // Create intensity curve based on transition state
    switch (CurrentTransitionState)
    {
        case ERealmTransitionState::FadingOut:
            return BaseIntensity * (TransitionProgress * 2.0f); // Ramp up

        case ERealmTransitionState::Transitioning:
            return BaseIntensity; // Full intensity

        case ERealmTransitionState::FadingIn:
            return BaseIntensity * (2.0f - (TransitionProgress * 2.0f)); // Ramp down

        default:
            return 0.0f;
    }
}

float UAuracronRealmTransitionComponent::CalculateAudioIntensity() const
{
    float BaseVolume = TransitionSettings.EffectIntensity;

    // Create audio curve based on transition state
    switch (CurrentTransitionState)
    {
        case ERealmTransitionState::FadingOut:
            return BaseVolume * FMath::Sin(TransitionProgress * PI * 0.5f); // Smooth fade in

        case ERealmTransitionState::Transitioning:
            return BaseVolume; // Full volume

        case ERealmTransitionState::FadingIn:
            return BaseVolume * FMath::Cos(TransitionProgress * PI * 0.5f); // Smooth fade out

        default:
            return 0.0f;
    }
}
