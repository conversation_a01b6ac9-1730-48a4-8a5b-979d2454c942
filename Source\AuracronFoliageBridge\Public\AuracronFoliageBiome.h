// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Biome System Header
// Bridge 4.4: Foliage - Biome System

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageProcedural.h"

// PCG includes for UE5.6
#include "PCGComponent.h"
#include "PCGGraph.h"
// Note: PCGBiome specific headers may not exist in base UE5.6
// These would be part of custom PCG Biome plugins if available

// Climate simulation includes
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialParameterCollection.h"

// Ecosystem includes
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeLayerInfoObject.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/RandomStream.h"

#include "AuracronFoliageBiome.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronFoliageBiome, Log, All);

// Forward declarations
class UAuracronFoliageBiomeManager;
class UAuracronBiomeDefinition;
class UAuracronClimateSimulator;

// =============================================================================
// BIOME TYPES AND ENUMS
// =============================================================================

// Biome types
UENUM(BlueprintType)
enum class EAuracronBiomeType : uint8
{
    Grassland               UMETA(DisplayName = "Grassland"),
    Forest                  UMETA(DisplayName = "Forest"),
    Desert                  UMETA(DisplayName = "Desert"),
    Tundra                  UMETA(DisplayName = "Tundra"),
    Wetland                 UMETA(DisplayName = "Wetland"),
    Mountain                UMETA(DisplayName = "Mountain"),
    Coastal                 UMETA(DisplayName = "Coastal"),
    Urban                   UMETA(DisplayName = "Urban"),
    Volcanic                UMETA(DisplayName = "Volcanic"),
    Magical                 UMETA(DisplayName = "Magical"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Climate types
UENUM(BlueprintType)
enum class EAuracronClimateType : uint8
{
    Tropical                UMETA(DisplayName = "Tropical"),
    Arid                    UMETA(DisplayName = "Arid"),
    Temperate               UMETA(DisplayName = "Temperate"),
    Continental             UMETA(DisplayName = "Continental"),
    Polar                   UMETA(DisplayName = "Polar"),
    Mediterranean           UMETA(DisplayName = "Mediterranean"),
    Oceanic                 UMETA(DisplayName = "Oceanic"),
    Subarctic               UMETA(DisplayName = "Subarctic"),
    Alpine                  UMETA(DisplayName = "Alpine"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Transition zone types
UENUM(BlueprintType)
enum class EAuracronTransitionZoneType : uint8
{
    Sharp                   UMETA(DisplayName = "Sharp"),
    Gradual                 UMETA(DisplayName = "Gradual"),
    Ecotone                 UMETA(DisplayName = "Ecotone"),
    Mosaic                  UMETA(DisplayName = "Mosaic"),
    Elevation               UMETA(DisplayName = "Elevation Based"),
    Moisture                UMETA(DisplayName = "Moisture Based"),
    Temperature             UMETA(DisplayName = "Temperature Based"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Species distribution patterns
UENUM(BlueprintType)
enum class EAuracronSpeciesDistributionPattern : uint8
{
    Random                  UMETA(DisplayName = "Random"),
    Clustered               UMETA(DisplayName = "Clustered"),
    Uniform                 UMETA(DisplayName = "Uniform"),
    Linear                  UMETA(DisplayName = "Linear"),
    Radial                  UMETA(DisplayName = "Radial"),
    Fractal                 UMETA(DisplayName = "Fractal"),
    Succession              UMETA(DisplayName = "Succession Based"),
    Competition             UMETA(DisplayName = "Competition Based"),
    Symbiotic               UMETA(DisplayName = "Symbiotic"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Ecosystem rules
UENUM(BlueprintType)
enum class EAuracronEcosystemRuleType : uint8
{
    Predation               UMETA(DisplayName = "Predation"),
    Competition             UMETA(DisplayName = "Competition"),
    Mutualism               UMETA(DisplayName = "Mutualism"),
    Parasitism              UMETA(DisplayName = "Parasitism"),
    Commensalism            UMETA(DisplayName = "Commensalism"),
    Succession              UMETA(DisplayName = "Succession"),
    Disturbance             UMETA(DisplayName = "Disturbance"),
    Migration               UMETA(DisplayName = "Migration"),
    Seasonal                UMETA(DisplayName = "Seasonal"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// CLIMATE DATA
// =============================================================================

/**
 * Climate Data
 * Environmental data for climate simulation
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronClimateData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temperature")
    float AverageTemperature = 20.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temperature")
    float TemperatureRange = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temperature")
    float SeasonalVariation = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temperature")
    float TemperatureTolerance = 15.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Precipitation")
    float AnnualPrecipitation = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Precipitation")
    float Precipitation = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Precipitation")
    float PrecipitationVariability = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Precipitation")
    float PrecipitationTolerance = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Precipitation")
    float DrySeason = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Humidity")
    float RelativeHumidity = 60.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Humidity")
    float HumidityRange = 20.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Humidity")
    float MoistureTolerance = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float WindSpeed = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    FVector WindDirection = FVector(1.0f, 0.0f, 0.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float WindVariability = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Solar")
    float SolarRadiation = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Solar")
    float DaylightHours = 12.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Solar")
    float CloudCover = 0.4f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Soil")
    float SoilMoisture = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Soil")
    float SoilFertility = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Soil")
    float SoilPH = 6.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elevation")
    float Elevation = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elevation")
    float Slope = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elevation")
    FVector Aspect = FVector(0.0f, 1.0f, 0.0f);

    FAuracronClimateData()
    {
        AverageTemperature = 20.0f;
        TemperatureRange = 10.0f;
        SeasonalVariation = 5.0f;
        AnnualPrecipitation = 1000.0f;
        PrecipitationVariability = 0.2f;
        DrySeason = 0.3f;
        RelativeHumidity = 60.0f;
        HumidityRange = 20.0f;
        WindSpeed = 5.0f;
        WindDirection = FVector(1.0f, 0.0f, 0.0f);
        WindVariability = 0.3f;
        SolarRadiation = 200.0f;
        DaylightHours = 12.0f;
        CloudCover = 0.4f;
        SoilMoisture = 0.5f;
        SoilFertility = 0.7f;
        SoilPH = 6.5f;
        Elevation = 100.0f;
        Slope = 5.0f;
        Aspect = FVector(0.0f, 1.0f, 0.0f);
    }
};

// =============================================================================
// SPECIES DATA
// =============================================================================

/**
 * Species Data
 * Data for individual species in biomes
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronSpeciesData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    FString SpeciesId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    FString SpeciesName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    TSoftObjectPtr<UStaticMesh> SpeciesMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    EAuracronSpeciesDistributionPattern DistributionPattern = EAuracronSpeciesDistributionPattern::Random;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abundance")
    float BaseAbundance = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abundance")
    float MaxAbundance = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abundance")
    float GrowthRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environmental Tolerance")
    float TemperatureTolerance = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environmental Tolerance")
    float MoistureTolerance = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environmental Tolerance")
    float SoilTolerance = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environmental Tolerance")
    float ElevationTolerance = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float MinSpacing = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float MaxSpacing = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float ClusterRadius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    int32 ClusterSize = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interactions")
    TArray<FString> CompetitorSpecies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interactions")
    TArray<FString> SymbioticSpecies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interactions")
    TArray<FString> PreySpecies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interactions")
    TArray<FString> PredatorSpecies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle")
    float LifeSpan = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle")
    float MaturityAge = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle")
    float ReproductionRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lifecycle")
    float MortalityRate = 0.01f;

    // Dynamic properties for ecosystem simulation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic")
    float Abundance = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic")
    float SurvivalRate = 0.9f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic")
    float ClusteringFactor = 1.0f;

    FAuracronSpeciesData()
    {
        DistributionPattern = EAuracronSpeciesDistributionPattern::Random;
        BaseAbundance = 1.0f;
        MaxAbundance = 10.0f;
        GrowthRate = 0.1f;
        TemperatureTolerance = 10.0f;
        MoistureTolerance = 0.3f;
        SoilTolerance = 0.5f;
        ElevationTolerance = 500.0f;
        MinSpacing = 100.0f;
        MaxSpacing = 500.0f;
        ClusterRadius = 1000.0f;
        ClusterSize = 10;
        LifeSpan = 100.0f;
        MaturityAge = 10.0f;
        ReproductionRate = 0.1f;
        MortalityRate = 0.01f;
    }
};

// =============================================================================
// ECOSYSTEM RULE DATA
// =============================================================================

/**
 * Ecosystem Rule Data
 * Rules governing ecosystem interactions
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronEcosystemRuleData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    FString RuleId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    FString RuleName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    EAuracronEcosystemRuleType RuleType = EAuracronEcosystemRuleType::Competition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    bool bEnabled = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    float RuleStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    float EffectStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Participants")
    TArray<FString> PrimarySpecies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Participants")
    TArray<FString> SecondarySpecies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float EffectRadius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    bool bRequiresProximity = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temporal")
    float ActivationDelay = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temporal")
    float Duration = -1.0f; // -1 = permanent

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temporal")
    bool bSeasonallyActive = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temporal")
    float LastUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temporal")
    float UpdateInterval = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Participants")
    TArray<FString> SourceSpeciesIds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Participants")
    TArray<FString> TargetSpeciesIds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
    float MutualismStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    float PopulationEffect = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    float GrowthRateEffect = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    float DistributionEffect = 0.0f;

    // Specific effect properties for ecosystem rules
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    float CompetitionIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    float MutualismStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
    float MinTemperature = -100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
    float MaxTemperature = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
    float MinMoisture = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
    float MaxMoisture = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
    TMap<FString, float> CustomParameters;

    FAuracronEcosystemRuleData()
    {
        RuleType = EAuracronEcosystemRuleType::Competition;
        bEnabled = true;
        RuleStrength = 1.0f;
        EffectRadius = 1000.0f;
        bRequiresProximity = true;
        ActivationDelay = 0.0f;
        Duration = -1.0f;
        bSeasonallyActive = false;
        PopulationEffect = 0.0f;
        GrowthRateEffect = 0.0f;
        DistributionEffect = 0.0f;
        MinTemperature = -100.0f;
        MaxTemperature = 100.0f;
        MinMoisture = 0.0f;
        MaxMoisture = 1.0f;
    }
};

// =============================================================================
// BIOME DEFINITION
// =============================================================================

/**
 * Biome Definition
 * Complete definition of a biome including climate, species, and rules
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronBiomeDefinition
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    FString BiomeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    FString BiomeName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    EAuracronBiomeType BiomeType = EAuracronBiomeType::Grassland;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    EAuracronClimateType ClimateType = EAuracronClimateType::Temperate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    FAuracronClimateData ClimateData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Location")
    FVector CenterLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Location")
    float InfluenceRadius = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Location")
    FBox BiomeBounds = FBox(ForceInit);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrain")
    FVector2D ElevationRange = FVector2D(0.0f, 1000.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrain")
    FVector2D SlopeRange = FVector2D(0.0f, 45.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transitions")
    float TransitionZoneWidth = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    TArray<FAuracronSpeciesData> DominantSpecies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    TArray<FAuracronSpeciesData> SubdominantSpecies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    TArray<FAuracronSpeciesData> RareSpecies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    TArray<FAuracronSpeciesData> SpeciesData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ecosystem")
    TArray<FAuracronEcosystemRuleData> EcosystemRules;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Succession")
    float SuccessionTimer = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Succession")
    float SuccessionInterval = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Species")
    int32 MaxSpeciesCount = 50;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ecosystem")
    TArray<FAuracronEcosystemRuleData> EcosystemRules;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transitions")
    EAuracronTransitionZoneType TransitionType = EAuracronTransitionZoneType::Gradual;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transitions")
    float TransitionWidth = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transitions")
    TArray<FString> AdjacentBiomes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float MinBiomeSize = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float MaxBiomeSize = 50000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float BiomeDensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temporal")
    bool bSeasonalChanges = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temporal")
    float SeasonalIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temporal")
    bool bSuccessionEnabled = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Temporal")
    float SuccessionRate = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    bool bDisturbanceEvents = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    float DisturbanceFrequency = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    float DisturbanceIntensity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Integration")
    TSoftObjectPtr<UPCGGraph> BiomePCGGraph;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Integration")
    TMap<FString, FString> PCGParameters;

    FAuracronBiomeDefinition()
    {
        BiomeType = EAuracronBiomeType::Grassland;
        ClimateType = EAuracronClimateType::Temperate;
        TransitionType = EAuracronTransitionZoneType::Gradual;
        TransitionWidth = 1000.0f;
        MinBiomeSize = 5000.0f;
        MaxBiomeSize = 50000.0f;
        BiomeDensity = 1.0f;
        bSeasonalChanges = true;
        SeasonalIntensity = 1.0f;
        bSuccessionEnabled = true;
        SuccessionRate = 0.01f;
        bDisturbanceEvents = false;
        DisturbanceFrequency = 0.1f;
        DisturbanceIntensity = 0.5f;
    }
};

// =============================================================================
// BIOME CONFIGURATION
// =============================================================================

/**
 * Disturbance event data
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronDisturbanceEvent
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    FString EventId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    FString BiomeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    float Intensity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    FVector Location = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    float Radius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    float StartTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    float Duration = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disturbance")
    bool bIsActive = false;

    FAuracronDisturbanceEvent()
    {
        EventId = TEXT("");
        BiomeId = TEXT("");
        Intensity = 0.5f;
        Location = FVector::ZeroVector;
        Radius = 1000.0f;
        StartTime = 0.0f;
        Duration = 10.0f;
        bIsActive = false;
    }
};

/**
 * Biome Configuration
 * Configuration for the biome system
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronBiomeConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome System")
    bool bEnableBiomeSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome System")
    bool bEnableClimateSimulation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome System")
    bool bEnableEcosystemRules = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome System")
    bool bEnableSpeciesInteractions = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome System")
    bool bEnableSuccession = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome System")
    bool bEnableDisturbanceEvents = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    float SimulationTimeStep = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    float ClimateUpdateInterval = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    float EcosystemUpdateInterval = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    float SpeciesUpdateInterval = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxSimulatedBiomes = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float SpatialIndexCellSize = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxSpeciesPerBiome = 50;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float SimulationRadius = 20000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncSimulation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConcurrentSimulations = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bEnableBiomeVisualization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowClimateData = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowSpeciesDistribution = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowEcosystemRules = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
    bool bShowTransitionZones = false;

    FAuracronBiomeConfiguration()
    {
        bEnableBiomeSystem = true;
        bEnableClimateSimulation = true;
        bEnableEcosystemRules = true;
        bEnableSpeciesInteractions = true;
        bEnableSuccession = true;
        bEnableDisturbanceEvents = false;
        SimulationTimeStep = 1.0f;
        ClimateUpdateInterval = 10.0f;
        EcosystemUpdateInterval = 5.0f;
        SpeciesUpdateInterval = 1.0f;
        MaxSimulatedBiomes = 10;
        MaxSpeciesPerBiome = 50;
        SimulationRadius = 20000.0f;
        bEnableAsyncSimulation = true;
        MaxConcurrentSimulations = 4;
        bEnableBiomeVisualization = true;
        bShowClimateData = false;
        bShowSpeciesDistribution = false;
        bShowEcosystemRules = false;
        bShowTransitionZones = false;
    }
};

// =============================================================================
// FOLIAGE BIOME MANAGER
// =============================================================================

/**
 * Foliage Biome Manager
 * Manager for the biome system including climate simulation and ecosystem rules
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONFOLIAGEBRIDGE_API UAuracronFoliageBiomeManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    static UAuracronFoliageBiomeManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void Initialize(const FAuracronBiomeConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void Tick(float DeltaTime);

    // Biome management
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    bool RegisterBiome(const FAuracronBiomeDefinition& BiomeDefinition);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    bool UnregisterBiome(const FString& BiomeId);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    FAuracronBiomeDefinition GetBiome(const FString& BiomeId) const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    TArray<FAuracronBiomeDefinition> GetAllBiomes() const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    bool UpdateBiome(const FAuracronBiomeDefinition& BiomeDefinition);

    // Biome queries
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    FString GetBiomeAtLocation(const FVector& Location) const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    TArray<FString> GetBiomesInArea(const FBox& Area) const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    float GetBiomeInfluence(const FString& BiomeId, const FVector& Location) const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    TArray<FString> GetAdjacentBiomes(const FString& BiomeId) const;

    // Climate simulation
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    FAuracronClimateData GetClimateAtLocation(const FVector& Location) const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void UpdateClimateSimulation(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void SetGlobalClimateParameters(float GlobalTemperature, float GlobalPrecipitation, float GlobalHumidity);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void SimulateSeasonalChanges(float SeasonProgress);

    // Species management
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    bool AddSpeciesToBiome(const FString& BiomeId, const FAuracronSpeciesData& SpeciesData);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    bool RemoveSpeciesFromBiome(const FString& BiomeId, const FString& SpeciesId);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    TArray<FAuracronSpeciesData> GetSpeciesInBiome(const FString& BiomeId) const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    TArray<FAuracronSpeciesData> GetSpeciesAtLocation(const FVector& Location) const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    float GetSpeciesAbundance(const FString& BiomeId, const FString& SpeciesId) const;

    // Ecosystem rules
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    bool AddEcosystemRule(const FString& BiomeId, const FAuracronEcosystemRuleData& RuleData);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    bool RemoveEcosystemRule(const FString& BiomeId, const FString& RuleId);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    TArray<FAuracronEcosystemRuleData> GetEcosystemRules(const FString& BiomeId) const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void UpdateEcosystemRules(float DeltaTime);

    // Succession and disturbance
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void UpdateSuccession(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void TriggerDisturbanceEvent(const FString& BiomeId, float Intensity, const FVector& Location, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void UpdateDisturbanceEvents(float DeltaTime);

    // PCG integration
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void GenerateBiomePCG(const FString& BiomeId, const FBox& Area);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void UpdateBiomePCGParameters(const FString& BiomeId, const TMap<FString, FString>& Parameters);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    UPCGComponent* GetBiomePCGComponent(const FString& BiomeId) const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void SetConfiguration(const FAuracronBiomeConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    FAuracronBiomeConfiguration GetConfiguration() const;

    // Statistics
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    int32 GetTotalBiomeCount() const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    int32 GetTotalSpeciesCount() const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    TMap<FString, int32> GetBiomeStatistics() const;

    // Debug visualization
    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Biome Manager")
    void DrawDebugVisualization(UWorld* World) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBiomeRegistered, FString, BiomeId, FAuracronBiomeDefinition, BiomeDefinition);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBiomeUnregistered, FString, BiomeId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSpeciesAdded, FString, BiomeId, FString, SpeciesId, FAuracronSpeciesData, SpeciesData);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSpeciesRemoved, FString, BiomeId, FString, SpeciesId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnDisturbanceEvent, FString, BiomeId, FVector, Location, float, Intensity);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBiomeRegistered OnBiomeRegistered;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBiomeUnregistered OnBiomeUnregistered;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnSpeciesAdded OnSpeciesAdded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnSpeciesRemoved OnSpeciesRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDisturbanceEvent OnDisturbanceEvent;

private:
    static UAuracronFoliageBiomeManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronBiomeConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Biome data
    TMap<FString, FAuracronBiomeDefinition> RegisteredBiomes;
    TMap<FString, TWeakObjectPtr<UPCGComponent>> BiomePCGComponents;
    TMap<FString, TArray<FAuracronDisturbanceEvent>> ActiveDisturbanceEvents;

    // Climate simulation
    float GlobalTemperature = 20.0f;
    float GlobalPrecipitation = 1000.0f;
    float GlobalHumidity = 60.0f;
    float SeasonProgress = 0.0f;
    float LastClimateUpdate = 0.0f;

    // Ecosystem simulation
    float LastEcosystemUpdate = 0.0f;
    float LastSpeciesUpdate = 0.0f;
    float LastSuccessionUpdate = 0.0f;

    // Spatial indexing
    TMap<FIntVector, TArray<FString>> BiomeSpatialIndex;
    int32 SpatialIndexResolution = 1000;

    // Random generation
    FRandomStream RandomStream;

    // Thread safety
    mutable FCriticalSection BiomeLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateBiomeId() const;
    void UpdateSpatialIndex();
    void SimulateSpeciesInteractions(const FString& BiomeId, float DeltaTime);
    void ApplyEcosystemRule(const FString& BiomeId, const FAuracronEcosystemRuleData& Rule, float DeltaTime);
    void ProcessSuccession(const FString& BiomeId, float DeltaTime);
    void ProcessDisturbance(const FString& BiomeId, float DeltaTime);
    FAuracronClimateData InterpolateClimate(const FVector& Location) const;
    float CalculateBiomeTransition(const FString& BiomeId, const FVector& Location) const;
    void LogBiomeStatistics() const;

    // Species management functions
    FAuracronSpeciesData* FindSpeciesData(const FString& BiomeId, const FString& SpeciesId);
    float CalculateCompetitionPressure(const FAuracronSpeciesData* SpeciesData, const FAuracronEcosystemRuleData& Rule) const;
    float CalculateMutualisticBenefit(const FAuracronSpeciesData* SpeciesData, const FAuracronEcosystemRuleData& Rule) const;
};
