// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Biome System Implementation
// Bridge 4.4: Foliage - Biome System

#include "AuracronFoliageBiome.h"

DEFINE_LOG_CATEGORY(LogAuracronFoliageBiome);
#include "AuracronFoliage.h"
#include "AuracronFoliageBridge.h"

// PCG includes for UE5.6
#include "PCGComponent.h"
#include "PCGGraph.h"
// Note: PCGBiome specific headers may not exist in base UE5.6
// These would be part of custom PCG Biome plugins if available

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/Texture2D.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Landscape includes
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/RandomStream.h"
#include "ProceduralNoise.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// =============================================================================
// FOLIAGE BIOME MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageBiomeManager* UAuracronFoliageBiomeManager::Instance = nullptr;

UAuracronFoliageBiomeManager* UAuracronFoliageBiomeManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageBiomeManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageBiomeManager::Initialize(const FAuracronBiomeConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Biome Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize random stream
    RandomStream.Initialize(FMath::Rand());

    // Initialize collections
    RegisteredBiomes.Empty();
    BiomePCGComponents.Empty();
    BiomeSpatialIndex.Empty();

    // Initialize climate simulation
    GlobalTemperature = 20.0f;
    GlobalPrecipitation = 1000.0f;
    GlobalHumidity = 60.0f;
    SeasonProgress = 0.0f;
    LastClimateUpdate = 0.0f;

    // Initialize ecosystem simulation
    LastEcosystemUpdate = 0.0f;
    LastSpeciesUpdate = 0.0f;
    LastSuccessionUpdate = 0.0f;

    // Initialize spatial indexing
    SpatialIndexResolution = 1000;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Biome Manager initialized with climate simulation: %s, ecosystem rules: %s"), 
                              Configuration.bEnableClimateSimulation ? TEXT("enabled") : TEXT("disabled"),
                              Configuration.bEnableEcosystemRules ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageBiomeManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    RegisteredBiomes.Empty();
    BiomePCGComponents.Empty();
    BiomeSpatialIndex.Empty();

    // Reset references
    ManagedWorld.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Biome Manager shutdown completed"));
}

bool UAuracronFoliageBiomeManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageBiomeManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update climate simulation
    if (Configuration.bEnableClimateSimulation)
    {
        LastClimateUpdate += DeltaTime;
        if (LastClimateUpdate >= Configuration.ClimateUpdateInterval)
        {
            UpdateClimateSimulation(DeltaTime);
            LastClimateUpdate = 0.0f;
        }
    }

    // Update ecosystem rules
    if (Configuration.bEnableEcosystemRules)
    {
        LastEcosystemUpdate += DeltaTime;
        if (LastEcosystemUpdate >= Configuration.EcosystemUpdateInterval)
        {
            UpdateEcosystemRules(DeltaTime);
            LastEcosystemUpdate = 0.0f;
        }
    }

    // Update species interactions
    if (Configuration.bEnableSpeciesInteractions)
    {
        LastSpeciesUpdate += DeltaTime;
        if (LastSpeciesUpdate >= Configuration.SpeciesUpdateInterval)
        {
            for (const auto& BiomePair : RegisteredBiomes)
            {
                SimulateSpeciesInteractions(BiomePair.Key, DeltaTime);
            }
            LastSpeciesUpdate = 0.0f;
        }
    }

    // Update succession
    if (Configuration.bEnableSuccession)
    {
        LastSuccessionUpdate += DeltaTime;
        if (LastSuccessionUpdate >= 60.0f) // Update succession every minute
        {
            UpdateSuccession(DeltaTime);
            LastSuccessionUpdate = 0.0f;
        }
    }

    // Update disturbance events
    if (Configuration.bEnableDisturbanceEvents)
    {
        UpdateDisturbanceEvents(DeltaTime);
    }
}

bool UAuracronFoliageBiomeManager::RegisterBiome(const FAuracronBiomeDefinition& BiomeDefinition)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Biome Manager not initialized"));
        return false;
    }

    if (BiomeDefinition.BiomeId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid biome ID"));
        return false;
    }

    FScopeLock Lock(&BiomeLock);

    if (RegisteredBiomes.Contains(BiomeDefinition.BiomeId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Biome already registered: %s"), *BiomeDefinition.BiomeId);
        return false;
    }

    // Check biome limit
    if (RegisteredBiomes.Num() >= Configuration.MaxSimulatedBiomes)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Maximum biome limit reached: %d"), Configuration.MaxSimulatedBiomes);
        return false;
    }

    RegisteredBiomes.Add(BiomeDefinition.BiomeId, BiomeDefinition);

    // Update spatial index
    UpdateSpatialIndex();

    OnBiomeRegistered.Broadcast(BiomeDefinition.BiomeId, BiomeDefinition);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Biome registered: %s (%s)"), 
                              *BiomeDefinition.BiomeId, *BiomeDefinition.BiomeName);

    return true;
}

bool UAuracronFoliageBiomeManager::UnregisterBiome(const FString& BiomeId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Biome Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&BiomeLock);

    if (!RegisteredBiomes.Contains(BiomeId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Biome not found: %s"), *BiomeId);
        return false;
    }

    // Remove PCG component if exists
    if (BiomePCGComponents.Contains(BiomeId))
    {
        if (UPCGComponent* Component = BiomePCGComponents[BiomeId].Get())
        {
            Component->DestroyComponent();
        }
        BiomePCGComponents.Remove(BiomeId);
    }

    RegisteredBiomes.Remove(BiomeId);

    // Update spatial index
    UpdateSpatialIndex();

    OnBiomeUnregistered.Broadcast(BiomeId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Biome unregistered: %s"), *BiomeId);

    return true;
}

FAuracronBiomeDefinition UAuracronFoliageBiomeManager::GetBiome(const FString& BiomeId) const
{
    FScopeLock Lock(&BiomeLock);

    if (const FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId))
    {
        return *BiomeDefinition;
    }

    return FAuracronBiomeDefinition();
}

TArray<FAuracronBiomeDefinition> UAuracronFoliageBiomeManager::GetAllBiomes() const
{
    FScopeLock Lock(&BiomeLock);

    TArray<FAuracronBiomeDefinition> AllBiomes;
    RegisteredBiomes.GenerateValueArray(AllBiomes);
    return AllBiomes;
}

bool UAuracronFoliageBiomeManager::UpdateBiome(const FAuracronBiomeDefinition& BiomeDefinition)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Biome Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&BiomeLock);

    if (!RegisteredBiomes.Contains(BiomeDefinition.BiomeId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Biome not found: %s"), *BiomeDefinition.BiomeId);
        return false;
    }

    RegisteredBiomes[BiomeDefinition.BiomeId] = BiomeDefinition;

    // Update spatial index
    UpdateSpatialIndex();

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Biome updated: %s"), *BiomeDefinition.BiomeId);

    return true;
}

FString UAuracronFoliageBiomeManager::GetBiomeAtLocation(const FVector& Location) const
{
    FScopeLock Lock(&BiomeLock);

    // Find the biome with the highest influence at this location
    FString BestBiome;
    float BestInfluence = 0.0f;

    for (const auto& BiomePair : RegisteredBiomes)
    {
        float Influence = GetBiomeInfluence(BiomePair.Key, Location);
        if (Influence > BestInfluence)
        {
            BestInfluence = Influence;
            BestBiome = BiomePair.Key;
        }
    }

    return BestBiome;
}

TArray<FString> UAuracronFoliageBiomeManager::GetBiomesInArea(const FBox& Area) const
{
    FScopeLock Lock(&BiomeLock);

    TArray<FString> BiomesInArea;

    for (const auto& BiomePair : RegisteredBiomes)
    {
        const FAuracronBiomeDefinition& Biome = BiomePair.Value;
        
        // Check if biome overlaps with area
        // In a real implementation, this would use proper spatial bounds
        // For now, we check influence at area center
        FVector AreaCenter = Area.GetCenter();
        float Influence = GetBiomeInfluence(BiomePair.Key, AreaCenter);
        
        if (Influence > 0.1f) // Threshold for inclusion
        {
            BiomesInArea.Add(BiomePair.Key);
        }
    }

    return BiomesInArea;
}

float UAuracronFoliageBiomeManager::GetBiomeInfluence(const FString& BiomeId, const FVector& Location) const
{
    FScopeLock Lock(&BiomeLock);

    const FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        return 0.0f;
    }

    // Calculate comprehensive biome influence based on multiple factors
    float TotalInfluence = 0.0f;
    
    // Factor 1: Distance to biome center (30% weight)
    float DistanceToCenter = FVector::Dist(Location, BiomeDefinition->CenterLocation);
    float MaxInfluenceRadius = BiomeDefinition->InfluenceRadius;
    float DistanceInfluence = 0.0f;
    
    if (DistanceToCenter <= MaxInfluenceRadius)
    {
        // Use smooth falloff curve
        float NormalizedDistance = DistanceToCenter / MaxInfluenceRadius;
        DistanceInfluence = FMath::Pow(1.0f - NormalizedDistance, 2.0f); // Quadratic falloff
    }
    TotalInfluence += DistanceInfluence * 0.3f;
    
    // Factor 2: Climate suitability (40% weight)
    FAuracronClimateData LocationClimate = GetClimateAtLocation(Location);
    
    // Temperature compatibility
    float TemperatureDiff = FMath::Abs(LocationClimate.AverageTemperature - BiomeDefinition->ClimateData.AverageTemperature);
    float TemperatureInfluence = FMath::Max(0.0f, 1.0f - (TemperatureDiff / BiomeDefinition->ClimateData.TemperatureTolerance));
    
    // Moisture compatibility
    float MoistureDiff = FMath::Abs(LocationClimate.SoilMoisture - BiomeDefinition->ClimateData.SoilMoisture);
    float MoistureInfluence = FMath::Max(0.0f, 1.0f - (MoistureDiff / BiomeDefinition->ClimateData.MoistureTolerance));
    
    // Precipitation compatibility
    float PrecipitationDiff = FMath::Abs(LocationClimate.Precipitation - BiomeDefinition->ClimateData.Precipitation);
    float PrecipitationInfluence = FMath::Max(0.0f, 1.0f - (PrecipitationDiff / BiomeDefinition->ClimateData.PrecipitationTolerance));
    
    float ClimateInfluence = (TemperatureInfluence + MoistureInfluence + PrecipitationInfluence) / 3.0f;
    TotalInfluence += ClimateInfluence * 0.4f;
    
    // Factor 3: Elevation suitability (20% weight)
    float ElevationInfluence = 0.0f;
    if (BiomeDefinition->ElevationRange.X <= Location.Z && Location.Z <= BiomeDefinition->ElevationRange.Y)
    {
        // Calculate how close to optimal elevation
        float OptimalElevation = (BiomeDefinition->ElevationRange.X + BiomeDefinition->ElevationRange.Y) * 0.5f;
        float ElevationDiff = FMath::Abs(Location.Z - OptimalElevation);
        float MaxElevationDiff = (BiomeDefinition->ElevationRange.Y - BiomeDefinition->ElevationRange.X) * 0.5f;
        ElevationInfluence = FMath::Max(0.0f, 1.0f - (ElevationDiff / MaxElevationDiff));
    }
    TotalInfluence += ElevationInfluence * 0.2f;
    
    // Factor 4: Terrain slope compatibility (10% weight)
    float TerrainInfluence = 1.0f; // Default to full influence if no terrain data
    if (UWorld* World = GEngine ? GEngine->GetCurrentPlayWorld() : nullptr)
    {
        // Sample terrain slope at location
        FVector SampleOffset(100.0f, 0.0f, 0.0f);
        FVector Location1 = Location + SampleOffset;
        FVector Location2 = Location - SampleOffset;
        
        FHitResult Hit1, Hit2;
        if (World->LineTraceSingleByChannel(Hit1, Location1 + FVector(0, 0, 1000), Location1 - FVector(0, 0, 1000), ECC_WorldStatic) &&
            World->LineTraceSingleByChannel(Hit2, Location2 + FVector(0, 0, 1000), Location2 - FVector(0, 0, 1000), ECC_WorldStatic))
        {
            float HeightDiff = FMath::Abs(Hit1.Location.Z - Hit2.Location.Z);
            float Slope = FMath::Atan2(HeightDiff, 200.0f) * (180.0f / PI); // Convert to degrees
            
            // Check if slope is within biome's preferred range
            if (Slope >= BiomeDefinition->SlopeRange.X && Slope <= BiomeDefinition->SlopeRange.Y)
            {
                float OptimalSlope = (BiomeDefinition->SlopeRange.X + BiomeDefinition->SlopeRange.Y) * 0.5f;
                float SlopeDiff = FMath::Abs(Slope - OptimalSlope);
                float MaxSlopeDiff = (BiomeDefinition->SlopeRange.Y - BiomeDefinition->SlopeRange.X) * 0.5f;
                TerrainInfluence = FMath::Max(0.0f, 1.0f - (SlopeDiff / FMath::Max(MaxSlopeDiff, 1.0f)));
            }
            else
            {
                TerrainInfluence = 0.0f;
            }
        }
    }
    TotalInfluence += TerrainInfluence * 0.1f;
    
    return FMath::Clamp(TotalInfluence, 0.0f, 1.0f);
}

TArray<FString> UAuracronFoliageBiomeManager::GetAdjacentBiomes(const FString& BiomeId) const
{
    FScopeLock Lock(&BiomeLock);

    const FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        return TArray<FString>();
    }

    return BiomeDefinition->AdjacentBiomes;
}

FAuracronClimateData UAuracronFoliageBiomeManager::GetClimateAtLocation(const FVector& Location) const
{
    if (!Configuration.bEnableClimateSimulation)
    {
        FAuracronClimateData DefaultClimate;
        return DefaultClimate;
    }

    return InterpolateClimate(Location);
}

void UAuracronFoliageBiomeManager::UpdateClimateSimulation(float DeltaTime)
{
    // Update seasonal progress
    SeasonProgress += DeltaTime / (365.0f * 24.0f * 3600.0f); // Assuming 1 year cycle
    if (SeasonProgress >= 1.0f)
    {
        SeasonProgress -= 1.0f;
    }

    // Simulate seasonal changes for all biomes
    if (Configuration.bEnableClimateSimulation)
    {
        SimulateSeasonalChanges(SeasonProgress);
    }
}

void UAuracronFoliageBiomeManager::SetGlobalClimateParameters(float GlobalTemp, float GlobalPrecip, float GlobalHumid)
{
    GlobalTemperature = GlobalTemp;
    GlobalPrecipitation = GlobalPrecip;
    GlobalHumidity = GlobalHumid;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Global climate updated: Temp=%.1f, Precip=%.1f, Humidity=%.1f"),
                              GlobalTemperature, GlobalPrecipitation, GlobalHumidity);
}

void UAuracronFoliageBiomeManager::SimulateSeasonalChanges(float InSeasonProgress)
{
    FScopeLock Lock(&BiomeLock);

    for (auto& BiomePair : RegisteredBiomes)
    {
        FAuracronBiomeDefinition& Biome = BiomePair.Value;

        if (!Biome.bSeasonalChanges)
        {
            continue;
        }

        // Calculate seasonal modifiers
        float SeasonalTemp = FMath::Sin(SeasonProgress * 2.0f * PI) * Biome.SeasonalIntensity;
        float SeasonalPrecip = FMath::Cos(SeasonProgress * 2.0f * PI) * Biome.SeasonalIntensity * 0.5f;

        // Apply seasonal changes to climate data
        Biome.ClimateData.AverageTemperature = GlobalTemperature + SeasonalTemp;
        Biome.ClimateData.AnnualPrecipitation = GlobalPrecipitation * (1.0f + SeasonalPrecip);
        Biome.ClimateData.RelativeHumidity = GlobalHumidity * (1.0f + SeasonalPrecip * 0.2f);
    }
}

bool UAuracronFoliageBiomeManager::AddSpeciesToBiome(const FString& BiomeId, const FAuracronSpeciesData& SpeciesData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Biome Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&BiomeLock);

    FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Biome not found: %s"), *BiomeId);
        return false;
    }

    // Check species limit
    int32 TotalSpecies = BiomeDefinition->DominantSpecies.Num() +
                        BiomeDefinition->SubdominantSpecies.Num() +
                        BiomeDefinition->RareSpecies.Num();

    if (TotalSpecies >= Configuration.MaxSpeciesPerBiome)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Max species limit reached for biome: %s"), *BiomeId);
        return false;
    }

    // Add species based on abundance
    if (SpeciesData.BaseAbundance >= 5.0f)
    {
        BiomeDefinition->DominantSpecies.Add(SpeciesData);
    }
    else if (SpeciesData.BaseAbundance >= 2.0f)
    {
        BiomeDefinition->SubdominantSpecies.Add(SpeciesData);
    }
    else
    {
        BiomeDefinition->RareSpecies.Add(SpeciesData);
    }

    OnSpeciesAdded.Broadcast(BiomeId, SpeciesData.SpeciesId, SpeciesData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Species added to biome %s: %s"), *BiomeId, *SpeciesData.SpeciesName);

    return true;
}

bool UAuracronFoliageBiomeManager::RemoveSpeciesFromBiome(const FString& BiomeId, const FString& SpeciesId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Biome Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&BiomeLock);

    FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Biome not found: %s"), *BiomeId);
        return false;
    }

    // Remove from all species arrays
    bool bRemoved = false;

    BiomeDefinition->DominantSpecies.RemoveAll([&SpeciesId, &bRemoved](const FAuracronSpeciesData& Species)
    {
        if (Species.SpeciesId == SpeciesId)
        {
            bRemoved = true;
            return true;
        }
        return false;
    });

    if (!bRemoved)
    {
        BiomeDefinition->SubdominantSpecies.RemoveAll([&SpeciesId, &bRemoved](const FAuracronSpeciesData& Species)
        {
            if (Species.SpeciesId == SpeciesId)
            {
                bRemoved = true;
                return true;
            }
            return false;
        });
    }

    if (!bRemoved)
    {
        BiomeDefinition->RareSpecies.RemoveAll([&SpeciesId, &bRemoved](const FAuracronSpeciesData& Species)
        {
            if (Species.SpeciesId == SpeciesId)
            {
                bRemoved = true;
                return true;
            }
            return false;
        });
    }

    if (bRemoved)
    {
        OnSpeciesRemoved.Broadcast(BiomeId, SpeciesId);
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Species removed from biome %s: %s"), *BiomeId, *SpeciesId);
    }

    return bRemoved;
}

TArray<FAuracronSpeciesData> UAuracronFoliageBiomeManager::GetSpeciesInBiome(const FString& BiomeId) const
{
    FScopeLock Lock(&BiomeLock);

    const FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        return TArray<FAuracronSpeciesData>();
    }

    TArray<FAuracronSpeciesData> AllSpecies;
    AllSpecies.Append(BiomeDefinition->DominantSpecies);
    AllSpecies.Append(BiomeDefinition->SubdominantSpecies);
    AllSpecies.Append(BiomeDefinition->RareSpecies);

    return AllSpecies;
}

TArray<FAuracronSpeciesData> UAuracronFoliageBiomeManager::GetSpeciesAtLocation(const FVector& Location) const
{
    FString BiomeId = GetBiomeAtLocation(Location);
    if (BiomeId.IsEmpty())
    {
        return TArray<FAuracronSpeciesData>();
    }

    return GetSpeciesInBiome(BiomeId);
}

float UAuracronFoliageBiomeManager::GetSpeciesAbundance(const FString& BiomeId, const FString& SpeciesId) const
{
    TArray<FAuracronSpeciesData> Species = GetSpeciesInBiome(BiomeId);

    for (const FAuracronSpeciesData& SpeciesData : Species)
    {
        if (SpeciesData.SpeciesId == SpeciesId)
        {
            return SpeciesData.BaseAbundance;
        }
    }

    return 0.0f;
}

void UAuracronFoliageBiomeManager::ValidateConfiguration()
{
    // Validate simulation settings
    Configuration.SimulationTimeStep = FMath::Max(0.1f, Configuration.SimulationTimeStep);
    Configuration.ClimateUpdateInterval = FMath::Max(1.0f, Configuration.ClimateUpdateInterval);
    Configuration.EcosystemUpdateInterval = FMath::Max(1.0f, Configuration.EcosystemUpdateInterval);
    Configuration.SpeciesUpdateInterval = FMath::Max(0.1f, Configuration.SpeciesUpdateInterval);

    // Validate performance settings
    Configuration.MaxSimulatedBiomes = FMath::Max(1, Configuration.MaxSimulatedBiomes);
    Configuration.MaxSpeciesPerBiome = FMath::Max(1, Configuration.MaxSpeciesPerBiome);
    Configuration.SimulationRadius = FMath::Max(1000.0f, Configuration.SimulationRadius);
    Configuration.MaxConcurrentSimulations = FMath::Max(1, Configuration.MaxConcurrentSimulations);
}

FString UAuracronFoliageBiomeManager::GenerateBiomeId() const
{
    return FString::Printf(TEXT("Biome_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          RandomStream.RandRange(1000, 9999));
}

void UAuracronFoliageBiomeManager::UpdateSpatialIndex()
{
    BiomeSpatialIndex.Empty();

    for (const auto& BiomePair : RegisteredBiomes)
    {
        const FAuracronBiomeDefinition& Biome = BiomePair.Value;

        // Calculate spatial index key based on biome bounds
        FBox BiomeBounds = BiomePair.Value.BiomeBounds;
        FVector BiomeCenter = BiomeBounds.GetCenter();
        
        // Create spatial grid index with configurable cell size
        float CellSize = Configuration.SpatialIndexCellSize > 0.0f ? Configuration.SpatialIndexCellSize : 10000.0f; // Default 10km cells
        
        FIntVector IndexKey = FIntVector(
            FMath::FloorToInt(BiomeCenter.X / CellSize),
            FMath::FloorToInt(BiomeCenter.Y / CellSize),
            FMath::FloorToInt(BiomeCenter.Z / CellSize)
        );
        
        // Also add neighboring cells if biome spans multiple cells
        FVector BiomeExtent = BiomeBounds.GetExtent();
        if (BiomeExtent.X > CellSize || BiomeExtent.Y > CellSize || BiomeExtent.Z > CellSize)
        {
            // Add biome to multiple cells if it's large enough
            FIntVector MinCell = FIntVector(
                FMath::FloorToInt((BiomeCenter.X - BiomeExtent.X) / CellSize),
                FMath::FloorToInt((BiomeCenter.Y - BiomeExtent.Y) / CellSize),
                FMath::FloorToInt((BiomeCenter.Z - BiomeExtent.Z) / CellSize)
            );
            FIntVector MaxCell = FIntVector(
                FMath::FloorToInt((BiomeCenter.X + BiomeExtent.X) / CellSize),
                FMath::FloorToInt((BiomeCenter.Y + BiomeExtent.Y) / CellSize),
                FMath::FloorToInt((BiomeCenter.Z + BiomeExtent.Z) / CellSize)
            );
            
            // Add to all overlapping cells
            for (int32 X = MinCell.X; X <= MaxCell.X; X++)
            {
                for (int32 Y = MinCell.Y; Y <= MaxCell.Y; Y++)
                {
                    for (int32 Z = MinCell.Z; Z <= MaxCell.Z; Z++)
                    {
                        FIntVector CellKey = FIntVector(X, Y, Z);
                        if (!BiomeSpatialIndex.Contains(CellKey))
                        {
                            BiomeSpatialIndex.Add(CellKey, TArray<FString>());
                        }
                        BiomeSpatialIndex[CellKey].AddUnique(BiomePair.Key);
                    }
                }
            }
            continue; // Skip the single cell addition below
        }

        if (!BiomeSpatialIndex.Contains(IndexKey))
        {
            BiomeSpatialIndex.Add(IndexKey, TArray<FString>());
        }

        BiomeSpatialIndex[IndexKey].Add(BiomePair.Key);
    }
}

void UAuracronFoliageBiomeManager::SimulateSpeciesInteractions(const FString& BiomeId, float DeltaTime)
{
    FScopeLock Lock(&BiomeLock);

    FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        return;
    }

    // Simulate interactions between species
    TArray<FAuracronSpeciesData> AllSpecies;
    AllSpecies.Append(BiomeDefinition->DominantSpecies);
    AllSpecies.Append(BiomeDefinition->SubdominantSpecies);
    AllSpecies.Append(BiomeDefinition->RareSpecies);

    for (int32 i = 0; i < AllSpecies.Num(); ++i)
    {
        FAuracronSpeciesData& Species1 = AllSpecies[i];

        for (int32 j = i + 1; j < AllSpecies.Num(); ++j)
        {
            FAuracronSpeciesData& Species2 = AllSpecies[j];

            // Check for competition
            if (Species1.CompetitorSpecies.Contains(Species2.SpeciesId))
            {
                float CompetitionEffect = -0.01f * DeltaTime;
                Species1.BaseAbundance = FMath::Max(0.1f, Species1.BaseAbundance + CompetitionEffect);
                Species2.BaseAbundance = FMath::Max(0.1f, Species2.BaseAbundance + CompetitionEffect);
            }

            // Check for symbiosis
            if (Species1.SymbioticSpecies.Contains(Species2.SpeciesId))
            {
                float SymbiosisEffect = 0.005f * DeltaTime;
                Species1.BaseAbundance = FMath::Min(Species1.MaxAbundance, Species1.BaseAbundance + SymbiosisEffect);
                Species2.BaseAbundance = FMath::Min(Species2.MaxAbundance, Species2.BaseAbundance + SymbiosisEffect);
            }

            // Check for predation
            if (Species1.PreySpecies.Contains(Species2.SpeciesId))
            {
                float PredationEffect = 0.002f * DeltaTime;
                Species1.BaseAbundance = FMath::Min(Species1.MaxAbundance, Species1.BaseAbundance + PredationEffect);
                Species2.BaseAbundance = FMath::Max(0.1f, Species2.BaseAbundance - PredationEffect * 2.0f);
            }
        }
    }
}

void UAuracronFoliageBiomeManager::ApplyEcosystemRule(const FString& BiomeId, const FAuracronEcosystemRuleData& Rule, float DeltaTime)
{
    if (!Rule.bEnabled)
    {
        return;
    }

    FScopeLock Lock(&BiomeLock);

    FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        return;
    }

    // Apply rule effects based on type
    switch (Rule.RuleType)
    {
        case EAuracronEcosystemRuleType::Competition:
            // Reduce abundance of competing species
            for (const FString& SpeciesId : Rule.PrimarySpecies)
            {
                // Find and modify species abundance based on competition
                if (FAuracronSpeciesData* SpeciesData = FindSpeciesData(BiomeId, SpeciesId))
                {
                    // Calculate competition pressure
                    float CompetitionPressure = CalculateCompetitionPressure(SpeciesData, Rule);
                    
                    // Reduce abundance based on competition strength
                    float AbundanceReduction = CompetitionPressure * Rule.EffectStrength * DeltaTime;
                    SpeciesData->Abundance = FMath::Max(0.0f, SpeciesData->Abundance - AbundanceReduction);
                    
                    // Modify growth rate due to resource competition
                    SpeciesData->GrowthRate *= (1.0f - CompetitionPressure * 0.5f);
                    
                    // Update spatial distribution (competition affects clustering)
                    SpeciesData->ClusteringFactor *= (1.0f - CompetitionPressure * 0.3f);
                    
                    UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Applied competition to species %s: abundance %.2f, growth rate %.2f"), 
                           *SpeciesId, SpeciesData->Abundance, SpeciesData->GrowthRate);
                }
            }
            break;

        case EAuracronEcosystemRuleType::Mutualism:
            // Increase abundance of mutualistic species
            for (const FString& SpeciesId : Rule.PrimarySpecies)
            {
                // Find and modify species abundance based on mutualism
                if (FAuracronSpeciesData* SpeciesData = FindSpeciesData(BiomeId, SpeciesId))
                {
                    // Calculate mutualistic benefit
                    float MutualisticBenefit = CalculateMutualisticBenefit(SpeciesData, Rule);
                    
                    // Increase abundance based on mutualistic relationship strength
                    float AbundanceIncrease = MutualisticBenefit * Rule.EffectStrength * DeltaTime;
                    SpeciesData->Abundance = FMath::Min(1.0f, SpeciesData->Abundance + AbundanceIncrease);
                    
                    // Enhance growth rate due to mutualistic benefits
                    SpeciesData->GrowthRate *= (1.0f + MutualisticBenefit * 0.3f);
                    
                    // Improve survival rate
                    SpeciesData->SurvivalRate = FMath::Min(1.0f, SpeciesData->SurvivalRate + MutualisticBenefit * 0.2f);
                    
                    // Enhance clustering (mutualistic species tend to grow together)
                    SpeciesData->ClusteringFactor *= (1.0f + MutualisticBenefit * 0.4f);
                    
                    UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Applied mutualism to species %s: abundance %.2f, growth rate %.2f"), 
                           *SpeciesId, SpeciesData->Abundance, SpeciesData->GrowthRate);
                }
            }
            break;

        case EAuracronEcosystemRuleType::Succession:
            // Gradually change species composition
            ProcessSuccession(BiomeId, DeltaTime);
            break;

        case EAuracronEcosystemRuleType::Disturbance:
            // Apply disturbance effects
            ProcessDisturbance(BiomeId, DeltaTime);
            break;

        default:
            break;
    }
}

void UAuracronFoliageBiomeManager::ProcessSuccession(const FString& BiomeId, float DeltaTime)
{
    FScopeLock Lock(&BiomeLock);

    FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition || !BiomeDefinition->bSuccessionEnabled)
    {
        return;
    }

    // Simulate ecological succession
    float SuccessionEffect = BiomeDefinition->SuccessionRate * DeltaTime;

    // Early succession species decrease
    for (FAuracronSpeciesData& Species : BiomeDefinition->RareSpecies)
    {
        if (Species.MaturityAge < 5.0f) // Early succession indicator
        {
            Species.BaseAbundance = FMath::Max(0.1f, Species.BaseAbundance - SuccessionEffect);
        }
    }

    // Late succession species increase
    for (FAuracronSpeciesData& Species : BiomeDefinition->DominantSpecies)
    {
        if (Species.MaturityAge > 20.0f) // Late succession indicator
        {
            Species.BaseAbundance = FMath::Min(Species.MaxAbundance, Species.BaseAbundance + SuccessionEffect * 0.5f);
        }
    }
}

void UAuracronFoliageBiomeManager::ProcessDisturbance(const FString& BiomeId, float DeltaTime)
{
    FScopeLock Lock(&BiomeLock);

    FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition || !BiomeDefinition->bDisturbanceEvents)
    {
        return;
    }

    // Check for random disturbance events
    float DisturbanceChance = BiomeDefinition->DisturbanceFrequency * DeltaTime;

    if (RandomStream.FRand() < DisturbanceChance)
    {
        // Trigger disturbance event
        FVector DisturbanceLocation = FVector::ZeroVector; // Would be calculated based on biome bounds
        float DisturbanceRadius = RandomStream.FRandRange(500.0f, 2000.0f);

        TriggerDisturbanceEvent(BiomeId, BiomeDefinition->DisturbanceIntensity, DisturbanceLocation, DisturbanceRadius);
    }
}

FAuracronClimateData UAuracronFoliageBiomeManager::InterpolateClimate(const FVector& Location) const
{
    // In a real implementation, this would interpolate climate data from multiple sources
    // For now, we return a climate based on the dominant biome at the location

    FString DominantBiome = GetBiomeAtLocation(Location);
    if (DominantBiome.IsEmpty())
    {
        FAuracronClimateData DefaultClimate;
        DefaultClimate.AverageTemperature = GlobalTemperature;
        DefaultClimate.AnnualPrecipitation = GlobalPrecipitation;
        DefaultClimate.RelativeHumidity = GlobalHumidity;
        return DefaultClimate;
    }

    const FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(DominantBiome);
    if (BiomeDefinition)
    {
        return BiomeDefinition->ClimateData;
    }

    FAuracronClimateData DefaultClimate;
    return DefaultClimate;
}

float UAuracronFoliageBiomeManager::CalculateBiomeTransition(const FString& BiomeId, const FVector& Location) const
{
    const FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        return 0.0f;
    }

    // Calculate comprehensive biome transition based on multiple factors
    float TransitionValue = 0.0f;
    
    // Factor 1: Distance to biome boundaries (50% weight)
    float DistanceToCenter = FVector::Dist(Location, BiomeDefinition->CenterLocation);
    float TransitionZoneRadius = BiomeDefinition->InfluenceRadius * BiomeDefinition->TransitionZoneWidth;
    float BoundaryDistance = BiomeDefinition->InfluenceRadius - DistanceToCenter;
    
    float BoundaryTransition = 0.0f;
    if (BoundaryDistance > 0.0f)
    {
        if (BoundaryDistance <= TransitionZoneRadius)
        {
            // Within transition zone - smooth falloff
            BoundaryTransition = BoundaryDistance / TransitionZoneRadius;
            // Apply smooth curve for natural transition
            BoundaryTransition = FMath::SmoothStep(0.0f, 1.0f, BoundaryTransition);
        }
        else
        {
            // Within core biome area
            BoundaryTransition = 1.0f;
        }
    }
    TransitionValue += BoundaryTransition * 0.5f;
    
    // Factor 2: Environmental gradients (30% weight)
    float GradientTransition = 0.0f;
    FAuracronClimateData LocationClimate = GetClimateAtLocation(Location);
    
    // Temperature gradient
    float TempGradient = FMath::Abs(LocationClimate.AverageTemperature - BiomeDefinition->ClimateData.AverageTemperature);
    float TempTransition = FMath::Max(0.0f, 1.0f - (TempGradient / BiomeDefinition->ClimateData.TemperatureTolerance));
    
    // Moisture gradient
    float MoistureGradient = FMath::Abs(LocationClimate.SoilMoisture - BiomeDefinition->ClimateData.SoilMoisture);
    float MoistureTransition = FMath::Max(0.0f, 1.0f - (MoistureGradient / BiomeDefinition->ClimateData.MoistureTolerance));
    
    GradientTransition = (TempTransition + MoistureTransition) * 0.5f;
    TransitionValue += GradientTransition * 0.3f;
    
    // Factor 3: Adjacent biome influence (20% weight)
    float AdjacentInfluence = 1.0f;
    TArray<FString> AdjacentBiomes = GetAdjacentBiomes(BiomeId);
    
    for (const FString& AdjacentBiomeId : AdjacentBiomes)
    {
        float AdjacentBiomeInfluence = GetBiomeInfluence(AdjacentBiomeId, Location);
        if (AdjacentBiomeInfluence > 0.5f)
        {
            // Strong influence from adjacent biome reduces transition strength
            AdjacentInfluence *= (1.0f - (AdjacentBiomeInfluence - 0.5f));
        }
    }
    TransitionValue += AdjacentInfluence * 0.2f;
    
    return FMath::Clamp(TransitionValue, 0.0f, 1.0f);
}

void UAuracronFoliageBiomeManager::LogBiomeStatistics() const
{
    int32 TotalSpecies = 0;
    for (const auto& BiomePair : RegisteredBiomes)
    {
        const FAuracronBiomeDefinition& Biome = BiomePair.Value;
        TotalSpecies += Biome.DominantSpecies.Num() + Biome.SubdominantSpecies.Num() + Biome.RareSpecies.Num();
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Biome Statistics: %d biomes, %d total species, %.1f global temp"),
                              RegisteredBiomes.Num(),
                              TotalSpecies,
                              GlobalTemperature);
}

FAuracronSpeciesData* UAuracronFoliageBiomeManager::FindSpeciesData(const FString& BiomeId, const FString& SpeciesId)
{
    if (FAuracronBiomeDefinition* BiomeData = RegisteredBiomes.Find(BiomeId))
    {
        // Search in dominant species
        for (FAuracronSpeciesData& Species : BiomeData->DominantSpecies)
        {
            if (Species.SpeciesId == SpeciesId)
            {
                return &Species;
            }
        }

        // Search in subdominant species
        for (FAuracronSpeciesData& Species : BiomeData->SubdominantSpecies)
        {
            if (Species.SpeciesId == SpeciesId)
            {
                return &Species;
            }
        }

        // Search in rare species
        for (FAuracronSpeciesData& Species : BiomeData->RareSpecies)
        {
            if (Species.SpeciesId == SpeciesId)
            {
                return &Species;
            }
        }
    }
    return nullptr;
}

float UAuracronFoliageBiomeManager::CalculateCompetitionPressure(const FAuracronSpeciesData* SpeciesData, const FAuracronEcosystemRuleData& Rule) const
{
    if (!SpeciesData)
    {
        return 0.0f;
    }

    // Calculate competition pressure based on species abundance and rule parameters
    float CompetitionFactor = Rule.CompetitionIntensity * SpeciesData->Abundance;
    return FMath::Clamp(CompetitionFactor, 0.0f, 1.0f);
}

float UAuracronFoliageBiomeManager::CalculateMutualisticBenefit(const FAuracronSpeciesData* SpeciesData, const FAuracronEcosystemRuleData& Rule) const
{
    if (!SpeciesData)
    {
        return 0.0f;
    }

    // Calculate mutualistic benefit based on species characteristics and rule parameters
    float MutualismFactor = Rule.MutualismStrength * (1.0f - SpeciesData->Abundance);
    return FMath::Clamp(MutualismFactor, 0.0f, 1.0f);
}

// =============================================================================
// ECOSYSTEM RULES IMPLEMENTATION
// =============================================================================

bool UAuracronFoliageBiomeManager::AddEcosystemRule(const FString& BiomeId, const FAuracronEcosystemRuleData& RuleData)
{
    if (BiomeId.IsEmpty())
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot add ecosystem rule: BiomeId is empty"));
        return false;
    }

    // Find or create biome definition
    FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        // Create new biome definition if it doesn't exist
        FAuracronBiomeDefinition NewBiomeDefinition;
        NewBiomeDefinition.BiomeId = BiomeId;
        NewBiomeDefinition.BiomeName = BiomeId; // Default name
        RegisteredBiomes.Add(BiomeId, NewBiomeDefinition);
        BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    }

    // Add the ecosystem rule
    BiomeDefinition->EcosystemRules.Add(RuleData);

    UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Added ecosystem rule '%s' to biome '%s'"),
        *RuleData.RuleId, *BiomeId);

    return true;
}

bool UAuracronFoliageBiomeManager::RemoveEcosystemRule(const FString& BiomeId, const FString& RuleId)
{
    if (BiomeId.IsEmpty() || RuleId.IsEmpty())
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot remove ecosystem rule: BiomeId or RuleId is empty"));
        return false;
    }

    FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (!BiomeDefinition)
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot remove ecosystem rule: Biome '%s' not found"), *BiomeId);
        return false;
    }

    // Find and remove the rule
    int32 RemovedCount = BiomeDefinition->EcosystemRules.RemoveAll([&RuleId](const FAuracronEcosystemRuleData& Rule)
    {
        return Rule.RuleId == RuleId;
    });

    if (RemovedCount > 0)
    {
        UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Removed ecosystem rule '%s' from biome '%s'"),
            *RuleId, *BiomeId);
        return true;
    }
    else
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Ecosystem rule '%s' not found in biome '%s'"),
            *RuleId, *BiomeId);
        return false;
    }
}

TArray<FAuracronEcosystemRuleData> UAuracronFoliageBiomeManager::GetEcosystemRules(const FString& BiomeId) const
{
    TArray<FAuracronEcosystemRuleData> Rules;

    if (BiomeId.IsEmpty())
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot get ecosystem rules: BiomeId is empty"));
        return Rules;
    }

    const FAuracronBiomeDefinition* BiomeDefinition = RegisteredBiomes.Find(BiomeId);
    if (BiomeDefinition)
    {
        Rules = BiomeDefinition->EcosystemRules;
    }
    else
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Biome '%s' not found"), *BiomeId);
    }

    return Rules;
}

void UAuracronFoliageBiomeManager::UpdateEcosystemRules(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update ecosystem rules for all biomes
    for (auto& BiomePair : RegisteredBiomes)
    {
        FAuracronBiomeDefinition& BiomeDefinition = BiomePair.Value;

        // Process each ecosystem rule
        for (FAuracronEcosystemRuleData& Rule : BiomeDefinition.EcosystemRules)
        {
            // Update rule timing
            Rule.LastUpdateTime += DeltaTime;

            // Apply rule effects if enough time has passed
            if (Rule.LastUpdateTime >= Rule.UpdateInterval)
            {
                ApplyEcosystemRuleEffects(BiomeDefinition, Rule);
                Rule.LastUpdateTime = 0.0f;
            }
        }
    }
}

void UAuracronFoliageBiomeManager::UpdateSuccession(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update succession for all biomes
    for (auto& BiomePair : RegisteredBiomes)
    {
        FAuracronBiomeDefinition& BiomeDefinition = BiomePair.Value;

        // Update succession timer
        BiomeDefinition.SuccessionTimer += DeltaTime;

        // Check if succession should occur
        if (BiomeDefinition.SuccessionTimer >= BiomeDefinition.SuccessionInterval)
        {
            ProcessBiomeSuccession(BiomeDefinition);
            BiomeDefinition.SuccessionTimer = 0.0f;
        }
    }
}

void UAuracronFoliageBiomeManager::TriggerDisturbanceEvent(const FString& BiomeId, float Intensity, const FVector& Location, float Radius)
{
    if (BiomeId.IsEmpty())
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot trigger disturbance event: BiomeId is empty"));
        return;
    }

    // Check if biome exists
    if (!RegisteredBiomes.Contains(BiomeId))
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot trigger disturbance event: Biome '%s' not found"), *BiomeId);
        return;
    }

    // Create disturbance event
    FAuracronDisturbanceEvent DisturbanceEvent;
    DisturbanceEvent.EventId = FGuid::NewGuid().ToString();
    DisturbanceEvent.BiomeId = BiomeId;
    DisturbanceEvent.Intensity = FMath::Clamp(Intensity, 0.0f, 1.0f);
    DisturbanceEvent.Location = Location;
    DisturbanceEvent.Radius = FMath::Max(Radius, 0.0f);
    DisturbanceEvent.StartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    DisturbanceEvent.Duration = 10.0f; // Default duration
    DisturbanceEvent.bIsActive = true;

    // Add to active disturbance events
    TArray<FAuracronDisturbanceEvent>& BiomeEvents = ActiveDisturbanceEvents.FindOrAdd(BiomeId);
    BiomeEvents.Add(DisturbanceEvent);

    UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Triggered disturbance event in biome '%s' at location %s with intensity %.2f"),
        *BiomeId, *Location.ToString(), Intensity);

    // Apply immediate effects
    ApplyDisturbanceEffects(DisturbanceEvent);
}

void UAuracronFoliageBiomeManager::UpdateDisturbanceEvents(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Update disturbance events for all biomes
    for (auto& EventsPair : ActiveDisturbanceEvents)
    {
        const FString& BiomeId = EventsPair.Key;
        TArray<FAuracronDisturbanceEvent>& BiomeEvents = EventsPair.Value;

        // Update active disturbance events
        for (int32 i = BiomeEvents.Num() - 1; i >= 0; i--)
        {
            FAuracronDisturbanceEvent& Event = BiomeEvents[i];

            // Check if event has expired
            if (CurrentTime - Event.StartTime >= Event.Duration)
            {
                // Event has ended
                Event.bIsActive = false;
                BiomeEvents.RemoveAt(i);

                UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Disturbance event '%s' in biome '%s' has ended"),
                    *Event.EventId, *BiomeId);
            }
            else
            {
                // Continue applying effects
                ApplyDisturbanceEffects(Event);
            }
        }
    }
}

void UAuracronFoliageBiomeManager::GenerateBiomePCG(const FString& BiomeId, const FBox& Area)
{
    if (BiomeId.IsEmpty())
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot generate biome PCG: BiomeId is empty"));
        return;
    }

    FAuracronBiomeData* BiomeData = BiomeDataMap.Find(BiomeId);
    if (!BiomeData)
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot generate biome PCG: Biome '%s' not found"), *BiomeId);
        return;
    }

    // Find or create PCG component for this biome
    UPCGComponent* PCGComponent = GetBiomePCGComponent(BiomeId);
    if (!PCGComponent)
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot generate biome PCG: PCG component not found for biome '%s'"), *BiomeId);
        return;
    }

    // Set PCG generation bounds
    PCGComponent->SetGenerationBounds(Area);

    // Trigger PCG generation
    PCGComponent->GenerateLocal(true);

    UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Generated PCG for biome '%s' in area %s"),
        *BiomeId, *Area.ToString());
}

void UAuracronFoliageBiomeManager::UpdateBiomePCGParameters(const FString& BiomeId, const TMap<FString, FString>& Parameters)
{
    if (BiomeId.IsEmpty())
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot update biome PCG parameters: BiomeId is empty"));
        return;
    }

    UPCGComponent* PCGComponent = GetBiomePCGComponent(BiomeId);
    if (!PCGComponent)
    {
        UE_LOG(LogAuracronFoliageBiome, Warning, TEXT("Cannot update biome PCG parameters: PCG component not found for biome '%s'"), *BiomeId);
        return;
    }

    // Update PCG parameters
    for (const auto& ParamPair : Parameters)
    {
        const FString& ParamName = ParamPair.Key;
        const FString& ParamValue = ParamPair.Value;

        // Set parameter value (this would need to be adapted based on actual PCG parameter system)
        // For now, we'll log the parameter update
        UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Updated PCG parameter '%s' to '%s' for biome '%s'"),
            *ParamName, *ParamValue, *BiomeId);
    }

    // Regenerate PCG with new parameters
    PCGComponent->GenerateLocal(true);
}

UPCGComponent* UAuracronFoliageBiomeManager::GetBiomePCGComponent(const FString& BiomeId) const
{
    if (BiomeId.IsEmpty())
    {
        return nullptr;
    }

    // Look for PCG component in the world
    UWorld* World = GetWorld();
    if (!World)
    {
        return nullptr;
    }

    // Search for PCG component with matching biome ID
    // This is a simplified implementation - in practice, you'd have a more sophisticated mapping
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && Actor->GetName().Contains(BiomeId))
        {
            UPCGComponent* PCGComponent = Actor->FindComponentByClass<UPCGComponent>();
            if (PCGComponent)
            {
                return PCGComponent;
            }
        }
    }

    return nullptr;
}

void UAuracronFoliageBiomeManager::SetConfiguration(const FAuracronBiomeConfiguration& NewConfiguration)
{
    Configuration = NewConfiguration;

    // Apply configuration changes
    ApplyConfigurationChanges();

    UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Updated biome manager configuration"));
}

FAuracronBiomeConfiguration UAuracronFoliageBiomeManager::GetConfiguration() const
{
    return Configuration;
}

int32 UAuracronFoliageBiomeManager::GetTotalBiomeCount() const
{
    return BiomeDataMap.Num();
}

int32 UAuracronFoliageBiomeManager::GetTotalSpeciesCount() const
{
    int32 TotalSpecies = 0;

    for (const auto& BiomePair : BiomeDataMap)
    {
        const FAuracronBiomeData& BiomeData = BiomePair.Value;
        TotalSpecies += BiomeData.SpeciesData.Num();
    }

    return TotalSpecies;
}

TMap<FString, int32> UAuracronFoliageBiomeManager::GetBiomeStatistics() const
{
    TMap<FString, int32> Statistics;

    for (const auto& BiomePair : BiomeDataMap)
    {
        const FString& BiomeId = BiomePair.Key;
        const FAuracronBiomeData& BiomeData = BiomePair.Value;

        Statistics.Add(BiomeId + TEXT("_SpeciesCount"), BiomeData.SpeciesData.Num());
        Statistics.Add(BiomeId + TEXT("_EcosystemRules"), BiomeData.EcosystemRules.Num());
        Statistics.Add(BiomeId + TEXT("_ActiveDisturbances"), BiomeData.ActiveDisturbanceEvents.Num());
    }

    // Add global statistics
    Statistics.Add(TEXT("TotalBiomes"), BiomeDataMap.Num());
    Statistics.Add(TEXT("TotalSpecies"), GetTotalSpeciesCount());

    return Statistics;
}

void UAuracronFoliageBiomeManager::EnableDebugVisualization(bool bEnable)
{
    bDebugVisualizationEnabled = bEnable;

    UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Debug visualization %s"),
        bEnable ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronFoliageBiomeManager::IsDebugVisualizationEnabled() const
{
    return bDebugVisualizationEnabled;
}

void UAuracronFoliageBiomeManager::DrawDebugVisualization(UWorld* World) const
{
    if (!bDebugVisualizationEnabled || !World)
    {
        return;
    }

    // Draw debug information for each biome
    for (const auto& BiomePair : BiomeDataMap)
    {
        const FString& BiomeId = BiomePair.Key;
        const FAuracronBiomeData& BiomeData = BiomePair.Value;

        // Draw biome boundaries (simplified)
        FVector BiomeCenter = BiomeData.BiomeBounds.GetCenter();
        FVector BiomeExtent = BiomeData.BiomeBounds.GetExtent();

        // Draw biome box
        DrawDebugBox(World, BiomeCenter, BiomeExtent, FColor::Green, false, -1.0f, 0, 2.0f);

        // Draw biome label
        DrawDebugString(World, BiomeCenter + FVector(0, 0, BiomeExtent.Z + 50.0f),
            FString::Printf(TEXT("Biome: %s\nSpecies: %d"), *BiomeId, BiomeData.SpeciesData.Num()),
            nullptr, FColor::White, -1.0f);

        // Draw active disturbance events
        for (const FAuracronDisturbanceEvent& Event : BiomeData.ActiveDisturbanceEvents)
        {
            DrawDebugSphere(World, Event.Location, Event.Radius, 12, FColor::Red, false, -1.0f, 0, 2.0f);
            DrawDebugString(World, Event.Location + FVector(0, 0, Event.Radius + 20.0f),
                FString::Printf(TEXT("Disturbance\nIntensity: %.2f"), Event.Intensity),
                nullptr, FColor::Red, -1.0f);
        }
    }
}

// =============================================================================
// PRIVATE HELPER FUNCTIONS
// =============================================================================

void UAuracronFoliageBiomeManager::ApplyEcosystemRuleEffects(FAuracronBiomeData& BiomeData, const FAuracronEcosystemRuleData& Rule)
{
    // Apply ecosystem rule effects to the biome
    for (FAuracronSpeciesData& Species : BiomeData.SpeciesData)
    {
        // Apply rule effects based on rule type
        switch (Rule.RuleType)
        {
            case EAuracronEcosystemRuleType::Competition:
                ApplyCompetitionEffects(Species, Rule);
                break;
            case EAuracronEcosystemRuleType::Mutualism:
                ApplyMutualismEffects(Species, Rule);
                break;
            case EAuracronEcosystemRuleType::Predation:
                ApplyPredationEffects(Species, Rule);
                break;
            default:
                break;
        }
    }
}

void UAuracronFoliageBiomeManager::ProcessBiomeSuccession(FAuracronBiomeData& BiomeData)
{
    // Process ecological succession for the biome
    for (FAuracronSpeciesData& Species : BiomeData.SpeciesData)
    {
        // Update species based on succession stage
        float SuccessionFactor = CalculateSuccessionFactor(BiomeData, Species);

        // Adjust species abundance based on succession
        Species.Abundance = FMath::Clamp(Species.Abundance * SuccessionFactor, 0.0f, 1.0f);

        // Update species maturity
        Species.Maturity = FMath::Min(Species.Maturity + 0.1f, 1.0f);
    }

    UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Processed succession for biome '%s'"), *BiomeData.BiomeId);
}

void UAuracronFoliageBiomeManager::ApplyDisturbanceEffects(const FAuracronDisturbanceEvent& Event)
{
    FAuracronBiomeData* BiomeData = BiomeDataMap.Find(Event.BiomeId);
    if (!BiomeData)
    {
        return;
    }

    // Apply disturbance effects to species within the affected area
    for (FAuracronSpeciesData& Species : BiomeData->SpeciesData)
    {
        // Calculate distance from disturbance center
        float Distance = FVector::Dist(Species.Location, Event.Location);

        if (Distance <= Event.Radius)
        {
            // Calculate effect intensity based on distance
            float DistanceFactor = 1.0f - (Distance / Event.Radius);
            float EffectIntensity = Event.Intensity * DistanceFactor;

            // Apply negative effects to species
            Species.Health = FMath::Max(Species.Health - EffectIntensity * 0.5f, 0.0f);
            Species.Abundance = FMath::Max(Species.Abundance - EffectIntensity * 0.3f, 0.0f);
        }
    }
}

void UAuracronFoliageBiomeManager::ApplyConfigurationChanges()
{
    // Apply configuration changes to all biomes
    for (auto& BiomePair : BiomeDataMap)
    {
        FAuracronBiomeData& BiomeData = BiomePair.Value;

        // Update biome parameters based on configuration
        BiomeData.SuccessionInterval = Configuration.DefaultSuccessionInterval;
        BiomeData.MaxSpeciesCount = Configuration.MaxSpeciesPerBiome;

        // Apply other configuration settings as needed
    }

    UE_LOG(LogAuracronFoliageBiome, Log, TEXT("Applied configuration changes to all biomes"));
}

void UAuracronFoliageBiomeManager::ApplyCompetitionEffects(FAuracronSpeciesData& Species, const FAuracronEcosystemRuleData& Rule)
{
    // Apply competition effects to reduce species abundance
    float CompetitionPressure = CalculateCompetitionPressure(&Species, Rule);
    Species.Abundance = FMath::Max(Species.Abundance - CompetitionPressure * 0.1f, 0.0f);
}

void UAuracronFoliageBiomeManager::ApplyMutualismEffects(FAuracronSpeciesData& Species, const FAuracronEcosystemRuleData& Rule)
{
    // Apply mutualism effects to increase species abundance
    float MutualismBenefit = CalculateMutualisticBenefit(&Species, Rule);
    Species.Abundance = FMath::Min(Species.Abundance + MutualismBenefit * 0.1f, 1.0f);
}

void UAuracronFoliageBiomeManager::ApplyPredationEffects(FAuracronSpeciesData& Species, const FAuracronEcosystemRuleData& Rule)
{
    // Apply predation effects based on species role
    if (Rule.TargetSpeciesIds.Contains(Species.SpeciesId))
    {
        // This species is prey - reduce abundance
        Species.Abundance = FMath::Max(Species.Abundance - Rule.EffectStrength * 0.15f, 0.0f);
    }
    else if (Rule.SourceSpeciesIds.Contains(Species.SpeciesId))
    {
        // This species is predator - potentially increase abundance
        Species.Abundance = FMath::Min(Species.Abundance + Rule.EffectStrength * 0.05f, 1.0f);
    }
}

float UAuracronFoliageBiomeManager::CalculateSuccessionFactor(const FAuracronBiomeData& BiomeData, const FAuracronSpeciesData& Species)
{
    // Calculate succession factor based on species characteristics and biome state
    float BaseFactor = 1.0f;

    // Early succession species decline over time
    if (Species.SuccessionStage == EAuracronSuccessionStage::Early)
    {
        BaseFactor = FMath::Max(1.0f - (BiomeData.SuccessionTimer / BiomeData.SuccessionInterval) * 0.5f, 0.5f);
    }
    // Late succession species increase over time
    else if (Species.SuccessionStage == EAuracronSuccessionStage::Late)
    {
        BaseFactor = FMath::Min(1.0f + (BiomeData.SuccessionTimer / BiomeData.SuccessionInterval) * 0.3f, 1.5f);
    }

    return BaseFactor;
}
